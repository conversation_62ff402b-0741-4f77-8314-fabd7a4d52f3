import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { getPendingApprovalsForUser, getApprovalStatistics } from '@/lib/data/approvals'

/**
 * GET - Get pending approvals for current user
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has approval permissions
    if (!['manager', 'senior-manager', 'hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Fetch pending approvals and statistics
    const [pendingApprovals, statistics] = await Promise.all([
      getPendingApprovalsForUser(currentUser.id),
      getApprovalStatistics(currentUser.id)
    ])

    return NextResponse.json({ 
      success: true, 
      data: {
        pendingApprovals,
        statistics
      }
    })

  } catch (error) {
    console.error('Error fetching pending approvals:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch pending approvals' 
    }, { status: 500 })
  }
}
