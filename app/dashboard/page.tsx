import { getManagerAppraisals, getManagerPerformanceStats, getEmployeesForManager } from "@/lib/data/index"
import { getCurrentUser } from "@/lib/auth"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { PerformanceSpiderGraph } from "@/components/performance-spider-graph"
import { ErrorBoundary } from "@/components/error-boundary"
import { AppraisalDashboardWrapper } from "@/components/appraisal-dashboard-wrapper"
import { Badge } from "@/components/ui/badge"
import { DashboardDebug } from "@/components/dashboard-debug"

import { Users, UserPlus, BarChart3, User, ExternalLink } from "lucide-react"
import Link from "next/link"
import type { EmployeeAppraisal, Employee } from "@/lib/types"

export default async function ManagerDashboardPage() {
  console.log('[DASHBOARD] Loading manager dashboard')

  let appraisals: EmployeeAppraisal[] = []
  let performanceStats
  let teamMembers: Employee[] = []

  try {
    const currentUser = await getCurrentUser()

    console.log('[DASHBOARD DEBUG] Current user:', {
      id: currentUser?.id,
      email: currentUser?.email,
      fullName: currentUser?.fullName,
      role: currentUser?.role
    })

    // Special logging for CJN Automation
    if (currentUser?.email === '<EMAIL>') {
      console.log('[CJN DASHBOARD DEBUG] CJN Automation dashboard load:', {
        userId: currentUser.id,
        role: currentUser.role,
        shouldShowTeam: currentUser.role === 'manager' || currentUser.role === 'hr-admin'
      })
    }

    // Load appraisals, performance stats, and team members in parallel
    const [appraisalsData, statsData, teamData] = await Promise.all([
      getManagerAppraisals(),
      getManagerPerformanceStats(),
      // Show team members for managers, senior-managers and hr-admins, but not super-admins (they see company overview instead)
      currentUser && (currentUser.role === 'manager' || currentUser.role === 'senior-manager' || currentUser.role === 'hr-admin') ? getEmployeesForManager(currentUser.id) : Promise.resolve([])
    ])

    appraisals = appraisalsData
    performanceStats = statsData
    teamMembers = teamData

    console.log('[DASHBOARD DEBUG] Loaded', appraisals.length, 'appraisals,', teamMembers.length, 'team members, and performance stats')
    console.log('[DASHBOARD DEBUG] Team members:', teamMembers.map(m => ({ name: m.fullName, dept: m.departmentName, active: m.active })))

    // Special logging for CJN Automation
    if (currentUser?.email === '<EMAIL>') {
      console.log('[CJN DASHBOARD DEBUG] CJN team data result:', {
        teamMembersCount: teamMembers.length,
        teamMembers: teamMembers.map(m => ({ id: m.id, name: m.fullName, managerId: m.managerId }))
      })
    }
  } catch (error) {
    console.log('[DASHBOARD] Error loading data:', error)
    appraisals = []
    teamMembers = []
    performanceStats = {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }

  return (
    <div className="mobile-section">
      {/* Debug Info - Development Only */}
      <DashboardDebug />

      {/* Page Header */}
      <div className="mb-4 sm:mb-6">
        <h1 className="mobile-heading-1 tracking-tight">Manager Dashboard</h1>
        <p className="mobile-body text-muted-foreground mt-1">July 2025 appraisal period overview and team management.</p>
      </div>

      {/* Performance Overview - Top Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3 sm:mb-4">
          <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          <h2 className="mobile-heading-2">Performance Overview</h2>
        </div>
        <ErrorBoundary>
          <PerformanceSpiderGraph 
            stats={performanceStats}
            title="Team Performance Distribution"
            description="Overview of your team's performance ratings and completion status"
          />
        </ErrorBoundary>
      </div>

      {/* Team Section - Bottom Section */}
      <div className="space-y-4 sm:space-y-6">
        <div className="flex items-center gap-2 mb-3 sm:mb-4">
          <Users className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          <h2 className="mobile-heading-2">My Team</h2>
        </div>

        {/* Team Members Overview */}
        {teamMembers.length === 0 ? (
          <Card className="text-center py-8 sm:py-12">
            <CardContent>
              <Users className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
              <h3 className="text-base sm:text-lg font-medium mb-2">No team members assigned</h3>
              <p className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 px-4">
                Get started by adding employees to your team to begin the appraisal process.
              </p>
              <Button asChild className="touch-target">
                <Link href="/dashboard/add-people">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add People
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Team Members Grid */}
            <Card>
              <CardHeader className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-4">
                <CardTitle className="flex items-center gap-2 mobile-heading-2">
                  <User className="h-4 w-4 sm:h-5 sm:w-5" />
                  Team Members ({teamMembers.length})
                </CardTitle>
                <Button variant="outline" size="sm" asChild className="touch-target w-full sm:w-auto">
                  <Link href="/dashboard/team">
                    View All
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                  {teamMembers.slice(0, 6).map((member) => (
                    <Link
                      key={member.id}
                      href={`/dashboard/employees/${member.id}/profile`}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors touch-target"
                    >
                      <div className="flex flex-col min-w-0 flex-1 mr-3">
                        <span className="font-medium text-blue-600 hover:text-blue-800 truncate mobile-body">
                          {member.fullName}
                        </span>
                        <span className="mobile-caption text-muted-foreground truncate">
                          {member.departmentName || 'No Department'}
                        </span>
                      </div>
                      <Badge
                        variant={member.active ? "default" : "secondary"}
                        className={`text-xs flex-shrink-0 ${member.active ? "bg-green-100 text-green-800" : ""}`}
                      >
                        {member.active ? "Active" : "Inactive"}
                      </Badge>
                    </Link>
                  ))}
                </div>
                {teamMembers.length > 6 && (
                  <div className="mt-4 text-center">
                    <Button variant="outline" asChild className="touch-target w-full sm:w-auto">
                      <Link href="/dashboard/team">
                        View {teamMembers.length - 6} more team members
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Appraisals Section */}
            <div className="space-y-4">
              <h3 className="mobile-heading-2">Team Appraisals</h3>
              {appraisals.length === 0 ? (
                <Card className="text-center py-6 sm:py-8">
                  <CardContent>
                    <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                    <h4 className="text-sm sm:text-base font-medium mb-2">No appraisals started</h4>
                    <p className="text-muted-foreground text-xs sm:text-sm px-4">
                      Appraisals for your team members will appear here once the appraisal period begins.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <AppraisalDashboardWrapper data={appraisals} />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
