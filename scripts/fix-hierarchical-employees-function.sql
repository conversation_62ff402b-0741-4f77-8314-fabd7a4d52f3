-- Fixed get_hierarchical_employees function for proper senior manager hierarchy
-- This function properly handles the manager hierarchy and multi-manager employee relationships

CREATE OR REPLACE FUNCTION get_hierarchical_employees(manager_user_id TEXT)
RETURNS TABLE(
  id TEXT,
  full_name TEXT,
  compensation NUMERIC,
  rate TEXT,
  department_id TEXT,
  department_name TEXT,
  manager_id TEXT,
  manager_name TEXT,
  active BOOLEAN,
  hierarchy_level INTEGER
) AS $$
BEGIN
  RETURN QUERY
  WITH RECURSIVE manager_hierarchy AS (
    -- Base case: The given manager
    SELECT 
      m.user_id,
      m.full_name,
      m.role,
      m.manager_id,
      1 as level
    FROM appy_managers m
    WHERE m.user_id = manager_user_id
      AND m.active = true
    
    UNION ALL
    
    -- Recursive case: Find all managers that report to managers in our hierarchy
    SELECT 
      m.user_id,
      m.full_name,
      m.role,
      m.manager_id,
      mh.level + 1
    FROM appy_managers m
    INNER JOIN manager_hierarchy mh ON m.manager_id = mh.user_id
    WHERE m.active = true
      AND mh.level < 10  -- Prevent infinite recursion
  ),
  
  hierarchical_employees AS (
    -- Find all employees that report to any manager in our hierarchy
    -- Using the new multi-manager system (appy_employee_managers table)
    SELECT DISTINCT
      e.id,
      e.full_name,
      e.compensation,
      e.rate,
      e.department_id,
      COALESCE(d.name, '') as department_name,
      em.manager_id,
      COALESCE(m.full_name, '') as manager_name,
      e.active,
      mh.level as hierarchy_level
    FROM appy_employees e
    INNER JOIN appy_employee_managers em ON e.id = em.employee_id
    INNER JOIN manager_hierarchy mh ON em.manager_id = mh.user_id
    LEFT JOIN appy_departments d ON e.department_id = d.id
    LEFT JOIN appy_managers m ON em.manager_id = m.user_id
    WHERE e.active = true
    
    UNION
    
    -- Also include employees using the legacy manager_id field (for backward compatibility)
    SELECT DISTINCT
      e.id,
      e.full_name,
      e.compensation,
      e.rate,
      e.department_id,
      COALESCE(d.name, '') as department_name,
      e.manager_id,
      COALESCE(m.full_name, '') as manager_name,
      e.active,
      mh.level as hierarchy_level
    FROM appy_employees e
    INNER JOIN manager_hierarchy mh ON e.manager_id = mh.user_id
    LEFT JOIN appy_departments d ON e.department_id = d.id
    LEFT JOIN appy_managers m ON e.manager_id = m.user_id
    WHERE e.active = true
      AND e.manager_id IS NOT NULL
      -- Only include if not already covered by the multi-manager system
      AND NOT EXISTS (
        SELECT 1 FROM appy_employee_managers em2 
        WHERE em2.employee_id = e.id AND em2.manager_id = e.manager_id
      )
  )
  
  SELECT 
    he.id,
    he.full_name,
    he.compensation,
    he.rate,
    he.department_id,
    he.department_name,
    he.manager_id,
    he.manager_name,
    he.active,
    he.hierarchy_level
  FROM hierarchical_employees he
  ORDER BY he.hierarchy_level, he.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_hierarchical_employees(TEXT) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION get_hierarchical_employees(TEXT) IS 
'Returns all employees in the hierarchy under a given manager, including employees managed by sub-managers. Handles both the new multi-manager system and legacy single-manager relationships. Properly supports senior manager hierarchy where senior managers can see all employees under their management chain.';
